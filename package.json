{"name": "aura-prompt-extension", "version": "1.0.0", "description": "AI-powered Chrome extension using Vue.js and Chrome's Prompt API", "private": true, "keywords": ["chrome-extension", "ai", "vue", "prompt-api"], "license": "MIT", "author": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "webpack --mode=development --watch", "build": "webpack --mode=production", "build:dev": "webpack --mode=development"}, "dependencies": {"vue": "^3.4.0", "lucide-vue-next": "^0.344.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@vue/compiler-sfc": "^3.4.0", "autoprefixer": "^10.4.17", "babel-loader": "^9.1.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.10.0", "html-webpack-plugin": "^5.6.0", "postcss": "^8.4.35", "postcss-loader": "^8.1.0", "style-loader": "^3.3.4", "tailwindcss": "^3.4.1", "vue-loader": "^17.4.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0"}}