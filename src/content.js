// Content script for extracting page content and generating smart suggestions

// Track content script readiness
let isContentScriptReady = false;

// Initialize content script when DOM is ready
function initializeContentScript() {
  isContentScriptReady = true;
  console.log('Aura Prompt content script initialized on:', window.location.href);

  // Notify background script that content script is ready
  try {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      url: window.location.href,
      title: document.title
    }).catch(() => {
      // Background script might not be ready, ignore error
    });
  } catch (error) {
    // Extension context might be invalid, ignore
  }
}

// Listen for messages from background script or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handle ping requests to verify content script is alive
  if (request.action === 'ping') {
    sendResponse({
      alive: true,
      ready: isContentScriptReady,
      url: window.location.href,
      title: document.title
    });
    return true;
  }

  if (request.action === 'extractContent') {
    try {
      if (!isContentScriptReady) {
        sendResponse({
          error: 'Content script not ready',
          url: window.location.href,
          title: document.title
        });
        return true;
      }

      const pageContent = extractPageContent();

      sendResponse({
        success: true,
        content: pageContent,
        url: window.location.href,
        title: document.title
      });
    } catch (error) {
      console.error('Error extracting content:', error);
      sendResponse({
        error: 'Failed to extract page content: ' + error.message,
        url: window.location.href,
        title: document.title
      });
    }
    return true; // Keep message channel open for async response
  }

  if (request.action === 'getPageImages') {
    try {
      const images = extractPageImages();
      sendResponse({
        success: true,
        images: images
      });
    } catch (error) {
      console.error('Error extracting images:', error);
      sendResponse({
        error: 'Failed to extract page images: ' + error.message
      });
    }
    return true;
  }
});

// Function to extract meaningful content from the page
function extractPageContent() {
  // Remove script and style elements
  const elementsToRemove = document.querySelectorAll('script, style, nav, header, footer, aside');
  const clonedDoc = document.cloneNode(true);
  
  elementsToRemove.forEach(el => {
    const clonedEl = clonedDoc.querySelector(el.tagName.toLowerCase());
    if (clonedEl) clonedEl.remove();
  });

  // Extract text from main content areas
  const contentSelectors = [
    'main',
    'article',
    '[role="main"]',
    '.content',
    '.main-content',
    '.post-content',
    '.entry-content',
    '.article-content',
    'body'
  ];

  let content = '';
  
  for (const selector of contentSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      content = extractTextFromElement(element);
      if (content.length > 100) break; // Found substantial content
    }
  }

  // Fallback: extract from body if no main content found
  if (!content || content.length < 100) {
    content = extractTextFromElement(document.body);
  }

  // Clean and limit content
  content = cleanText(content);
  
  // Limit to ~2000 characters to avoid token limits
  if (content.length > 2000) {
    content = content.substring(0, 2000) + '...';
  }

  return content;
}

// Helper function to extract text from an element
function extractTextFromElement(element) {
  if (!element) return '';
  
  // Clone element to avoid modifying original
  const clone = element.cloneNode(true);
  
  // Remove unwanted elements
  const unwanted = clone.querySelectorAll('script, style, nav, header, footer, aside, .sidebar, .menu, .navigation, .ads, .advertisement');
  unwanted.forEach(el => el.remove());
  
  return clone.innerText || clone.textContent || '';
}

// Helper function to clean extracted text
function cleanText(text) {
  return text
    .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
    .replace(/\n\s*\n/g, '\n') // Remove empty lines
    .trim();
}

// Function to extract images from the page for analysis
function extractPageImages() {
  const images = [];
  const imageElements = document.querySelectorAll('img');

  imageElements.forEach((img, index) => {
    // Skip very small images (likely icons or decorative)
    if (img.naturalWidth < 100 || img.naturalHeight < 100) return;

    // Skip images without src
    if (!img.src) return;

    // Skip data URLs that are too small (likely icons)
    if (img.src.startsWith('data:') && img.src.length < 1000) return;

    const rect = img.getBoundingClientRect();

    // Skip images that are not visible
    if (rect.width === 0 || rect.height === 0) return;

    images.push({
      id: `img-${index}`,
      src: img.src,
      alt: img.alt || '',
      width: img.naturalWidth,
      height: img.naturalHeight,
      position: {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
        height: rect.height
      },
      // Additional context
      parentText: getImageContext(img),
      isMainContent: isInMainContent(img)
    });
  });

  // Sort by size (larger images first) and main content priority
  images.sort((a, b) => {
    if (a.isMainContent && !b.isMainContent) return -1;
    if (!a.isMainContent && b.isMainContent) return 1;
    return (b.width * b.height) - (a.width * a.height);
  });

  return images.slice(0, 20); // Limit to 20 images max
}

// Get text context around an image
function getImageContext(img) {
  const parent = img.closest('figure, div, article, section') || img.parentElement;
  if (!parent) return '';

  // Look for captions
  const caption = parent.querySelector('figcaption, .caption, .image-caption');
  if (caption) return caption.textContent.trim();

  // Look for nearby text
  const siblings = Array.from(parent.children);
  const imgIndex = siblings.indexOf(img);

  let context = '';
  // Check previous sibling
  if (imgIndex > 0) {
    const prevText = siblings[imgIndex - 1].textContent;
    if (prevText && prevText.length < 200) {
      context += prevText.trim() + ' ';
    }
  }

  // Check next sibling
  if (imgIndex < siblings.length - 1) {
    const nextText = siblings[imgIndex + 1].textContent;
    if (nextText && nextText.length < 200) {
      context += nextText.trim();
    }
  }

  return context.trim();
}

// Check if image is in main content area
function isInMainContent(img) {
  const mainSelectors = [
    'main',
    'article',
    '[role="main"]',
    '.content',
    '.main-content',
    '.post-content',
    '.entry-content',
    '.article-content'
  ];

  for (const selector of mainSelectors) {
    if (img.closest(selector)) {
      return true;
    }
  }

  return false;
}

// Static fallback suggestions for when AI generation fails
function getFallbackSuggestions() {
  return [
    "Summarize this page",
    "What are the main points?",
    "Explain this in simple terms"
  ];
}

// Initialize content script when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  // DOM is already ready
  initializeContentScript();
}
