<template>
  <div class="voice-input">
    <!-- Voice Input Header -->
    <div class="flex items-center gap-2 mb-4">
      <Mic class="w-5 h-5 text-primary"/>
      <h3 class="text-lg font-semibold">Voice Commands</h3>
    </div>

    <!-- Permission Status -->
    <div v-if="!hasPermission && !permissionDenied && !permissionRequested" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center gap-2 text-blue-700">
        <Info class="w-4 h-4"/>
        <span class="font-medium">Microphone Access Required</span>
      </div>
      <p class="text-sm mt-1 text-blue-600">
        Click the microphone button to enable voice commands. Your browser will ask for permission.
      </p>
    </div>

    <!-- Permission Denied -->
    <div v-if="permissionDenied" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center gap-2 text-red-700">
        <AlertTriangle class="w-4 h-4"/>
        <span class="font-medium">Microphone Access Denied</span>
      </div>
      <p class="text-sm mt-1 text-red-600">
        Please click the microphone icon in your browser's address bar and allow microphone access, then try again.
      </p>
    </div>

    <!-- Recording Controls -->
    <div class="space-y-4">
      <!-- Main Recording Button -->
      <div class="flex justify-center">
        <button
          @click="toggleRecording"
          :class="[
            'w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200',
            isRecording
              ? 'bg-red-500 hover:bg-red-600 animate-pulse'
              : 'bg-primary hover:bg-primary/90',
            'cursor-pointer'
          ]"
        >
          <Mic v-if="!isRecording" class="w-8 h-8 text-white"/>
          <Square v-else class="w-6 h-6 text-white"/>
        </button>
      </div>

      <!-- Recording Status -->
      <div class="text-center">
        <p v-if="!isRecording && !isProcessing" class="text-sm text-muted-foreground">
          {{ hasPermission ? 'Click to start recording' : 'Click to start recording (will request microphone access)' }}
        </p>
        <p v-if="isRecording" class="text-sm text-red-600 font-medium">
          🔴 Recording... ({{ recordingDuration }}s)
        </p>
        <p v-if="isProcessing" class="text-sm text-blue-600 font-medium">
          <Loader2 class="w-4 h-4 inline animate-spin mr-1"/>
          Processing audio...
        </p>
      </div>

      <!-- Recording Duration Limit -->
      <div v-if="isRecording" class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-red-500 h-2 rounded-full transition-all duration-1000"
          :style="{ width: `${(recordingDuration / maxRecordingDuration) * 100}%` }"
        ></div>
      </div>

      <!-- Quick Commands -->
      <div v-if="hasPermission && !isRecording" class="space-y-2">
        <p class="text-sm font-medium">Quick Commands:</p>
        <div class="grid grid-cols-1 gap-2">
          <button
            v-for="command in quickCommands"
            :key="command.text"
            @click="sendTextQuery(command.text)"
            class="text-left p-2 text-sm bg-muted hover:bg-muted/80 rounded-md transition-colors"
          >
            "{{ command.text }}"
          </button>
        </div>
      </div>
    </div>

    <!-- Recent Voice Queries -->
    <div v-if="recentQueries.length > 0" class="mt-6 space-y-2">
      <p class="text-sm font-medium">Recent Voice Queries:</p>
      <div class="space-y-1 max-h-32 overflow-y-auto">
        <div 
          v-for="(query, index) in recentQueries" 
          :key="index"
          class="text-xs p-2 bg-muted rounded text-muted-foreground"
        >
          "{{ query }}"
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
      <div class="flex items-center gap-2 text-destructive">
        <AlertTriangle class="w-4 h-4"/>
        <span class="font-medium">Voice Command Failed</span>
      </div>
      <p class="text-sm mt-1">{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { Mic, Square, Loader2, AlertTriangle, Info } from 'lucide-vue-next'
import { AudioRecorder } from '../utils/multimodal.js'

export default {
  name: 'VoiceInput',
  components: {
    Mic,
    Square,
    Loader2,
    AlertTriangle,
    Info
  },
  props: {
    pageContent: {
      type: String,
      default: ''
    },
    onVoiceQuery: {
      type: Function,
      default: () => {}
    }
  },
  emits: ['voice-query'],
  setup(props, { emit }) {
    const hasPermission = ref(false)
    const permissionDenied = ref(false)
    const permissionRequested = ref(false)
    const isRecording = ref(false)
    const isProcessing = ref(false)
    const recordingDuration = ref(0)
    const maxRecordingDuration = ref(30) // 30 seconds max
    const error = ref('')
    const recentQueries = ref([])

    const audioRecorder = ref(null)
    let recordingTimer = null
    let durationTimer = null

    const quickCommands = ref([
      { text: "Summarize the key points" },
      { text: "What are the main arguments?" },
      { text: "Explain this in simple terms" },
      { text: "What questions does this raise?" }
    ])

    onMounted(async () => {
      audioRecorder.value = new AudioRecorder()
      await checkMicrophonePermission()
    })

    onUnmounted(() => {
      if (audioRecorder.value) {
        audioRecorder.value.cleanup()
      }
      clearTimers()
    })

    const checkMicrophonePermission = async () => {
      try {
        // Check if microphone permission API is available
        if ('permissions' in navigator) {
          const permission = await navigator.permissions.query({ name: 'microphone' })

          if (permission.state === 'granted') {
            hasPermission.value = true
          } else if (permission.state === 'denied') {
            permissionDenied.value = true
          }

          // Listen for permission changes
          permission.onchange = () => {
            hasPermission.value = permission.state === 'granted'
            permissionDenied.value = permission.state === 'denied'
            permissionRequested.value = true
          }
        }
      } catch (err) {
        console.warn('Permission API not supported or microphone permission not available:', err)
        // Permission API might not support microphone in extensions, that's okay
      }
    }

    const toggleRecording = async () => {
      if (isRecording.value) {
        await stopRecording()
      } else {
        await startRecording()
      }
    }

    const startRecording = async () => {
      try {
        error.value = ''
        permissionRequested.value = true

        await audioRecorder.value.startRecording()

        hasPermission.value = true
        permissionDenied.value = false
        isRecording.value = true
        recordingDuration.value = 0

        // Start duration timer
        durationTimer = setInterval(() => {
          recordingDuration.value++

          // Auto-stop at max duration
          if (recordingDuration.value >= maxRecordingDuration.value) {
            stopRecording()
          }
        }, 1000)

      } catch (err) {
        error.value = err.message
        permissionDenied.value = true
        hasPermission.value = false
        console.error('Microphone access error:', err)
      }
    }

    const stopRecording = async () => {
      if (!isRecording.value) return

      try {
        isRecording.value = false
        isProcessing.value = true
        clearTimers()

        const audioBlob = await audioRecorder.value.stopRecording()
        
        // Process the audio with AI
        await processVoiceInput(audioBlob)

      } catch (err) {
        error.value = `Recording failed: ${err.message}`
      } finally {
        isProcessing.value = false
      }
    }

    const processVoiceInput = async (audioBlob) => {
      try {
        // Prepare multimodal inputs with audio
        const multimodalInputs = [{
          type: 'audio',
          data: audioBlob
        }]

        // Send to background script for processing
        const response = await new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({
            action: 'promptAPI',
            prompt: 'Please transcribe this audio and then respond to the question or command in the context of the current article.',
            context: props.pageContent,
            multimodalInputs: multimodalInputs
          }, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response.success) {
          // Extract transcription if possible (this would need to be handled by the AI)
          // For now, we'll emit the full response
          emit('voice-query', {
            type: 'voice',
            query: 'Voice command', // We'd need to extract this from the response
            response: response.response
          })

          // Add to recent queries (simplified)
          recentQueries.value.unshift('Voice command')
          if (recentQueries.value.length > 5) {
            recentQueries.value.pop()
          }

        } else {
          throw new Error(response.error || 'Voice processing failed')
        }

      } catch (err) {
        error.value = `Voice processing failed: ${err.message}`
        console.error('Voice processing error:', err)
      }
    }

    const sendTextQuery = async (queryText) => {
      try {
        error.value = ''
        
        // Send text query using the same voice query template
        const response = await new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({
            action: 'promptAPI',
            prompt: queryText,
            context: props.pageContent
          }, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response.success) {
          emit('voice-query', {
            type: 'text',
            query: queryText,
            response: response.response
          })

          // Add to recent queries
          recentQueries.value.unshift(queryText)
          if (recentQueries.value.length > 5) {
            recentQueries.value.pop()
          }
        } else {
          throw new Error(response.error || 'Query failed')
        }

      } catch (err) {
        error.value = err.message
      }
    }

    const clearTimers = () => {
      if (recordingTimer) {
        clearTimeout(recordingTimer)
        recordingTimer = null
      }
      if (durationTimer) {
        clearInterval(durationTimer)
        durationTimer = null
      }
    }

    return {
      hasPermission,
      permissionDenied,
      permissionRequested,
      isRecording,
      isProcessing,
      recordingDuration,
      maxRecordingDuration,
      error,
      recentQueries,
      quickCommands,
      toggleRecording,
      sendTextQuery
    }
  }
}
</script>

<style scoped>
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
