<template>
  <div class="source-comparator">
    <!-- Header -->
    <div class="flex items-center gap-2 mb-4">
      <GitCompare class="w-5 h-5 text-primary"/>
      <h3 class="text-lg font-semibold">Source Comparator</h3>
    </div>

    <p class="text-sm text-muted-foreground mb-4">
      Compare images from different news sources covering the same event to analyze visual framing and bias.
    </p>

    <!-- Image Selection -->
    <div class="space-y-4">
      <!-- Source 1 -->
      <div class="border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <h4 class="font-medium">Source 1</h4>
        </div>
        
        <div v-if="!selectedImages.source1" class="space-y-2">
          <input 
            ref="fileInput1"
            type="file" 
            accept="image/*" 
            @change="(e) => handleImageUpload(e, 'source1')"
            class="hidden"
          />
          <button 
            @click="$refs.fileInput1.click()"
            class="w-full p-4 border-2 border-dashed border-border rounded-lg hover:border-primary transition-colors flex items-center justify-center gap-2"
          >
            <Upload class="w-4 h-4"/>
            Upload first image
          </button>
          
          <div class="text-xs text-muted-foreground">
            <Input 
              v-model="sourceUrls.source1"
              placeholder="Source URL (optional)"
              class="w-full"
            />
          </div>
        </div>

        <div v-else class="space-y-2">
          <div class="relative">
            <img 
              :src="selectedImages.source1.preview" 
              :alt="selectedImages.source1.name"
              class="w-full max-h-32 object-contain rounded border"
            />
            <button 
              @click="removeImage('source1')"
              class="absolute top-1 right-1 p-1 bg-background border rounded-full hover:bg-muted"
            >
              <X class="w-3 h-3"/>
            </button>
          </div>
          <Input 
            v-model="sourceUrls.source1"
            placeholder="Source URL"
            class="w-full text-xs"
          />
        </div>
      </div>

      <!-- Source 2 -->
      <div class="border rounded-lg p-4">
        <div class="flex items-center gap-2 mb-3">
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <h4 class="font-medium">Source 2</h4>
        </div>
        
        <div v-if="!selectedImages.source2" class="space-y-2">
          <input 
            ref="fileInput2"
            type="file" 
            accept="image/*" 
            @change="(e) => handleImageUpload(e, 'source2')"
            class="hidden"
          />
          <button 
            @click="$refs.fileInput2.click()"
            class="w-full p-4 border-2 border-dashed border-border rounded-lg hover:border-primary transition-colors flex items-center justify-center gap-2"
          >
            <Upload class="w-4 h-4"/>
            Upload second image
          </button>
          
          <div class="text-xs text-muted-foreground">
            <Input 
              v-model="sourceUrls.source2"
              placeholder="Source URL (optional)"
              class="w-full"
            />
          </div>
        </div>

        <div v-else class="space-y-2">
          <div class="relative">
            <img 
              :src="selectedImages.source2.preview" 
              :alt="selectedImages.source2.name"
              class="w-full max-h-32 object-contain rounded border"
            />
            <button 
              @click="removeImage('source2')"
              class="absolute top-1 right-1 p-1 bg-background border rounded-full hover:bg-muted"
            >
              <X class="w-3 h-3"/>
            </button>
          </div>
          <Input 
            v-model="sourceUrls.source2"
            placeholder="Source URL"
            class="w-full text-xs"
          />
        </div>
      </div>
    </div>

    <!-- Event Context -->
    <div class="mt-4 space-y-2">
      <label class="text-sm font-medium">Event/Topic Context:</label>
      <Input 
        v-model="eventContext"
        placeholder="e.g., 'Climate protest in Berlin' or 'Tech conference keynote'"
        class="w-full"
      />
    </div>

    <!-- Analysis Type -->
    <div class="mt-4 space-y-2">
      <label class="text-sm font-medium">Analysis Focus:</label>
      <select 
        v-model="analysisType" 
        class="w-full p-2 border rounded-md bg-background"
      >
        <option value="general">General Comparison</option>
        <option value="bias">Editorial Bias & Framing</option>
        <option value="emotion">Emotional Tone</option>
        <option value="narrative">Visual Narrative</option>
      </select>
    </div>

    <!-- Compare Button -->
    <Button 
      @click="compareImages"
      :disabled="!canCompare || isComparing"
      class="w-full mt-4"
    >
      <Loader2 v-if="isComparing" class="w-4 h-4 mr-2 animate-spin"/>
      <GitCompare v-else class="w-4 h-4 mr-2"/>
      {{ isComparing ? 'Analyzing...' : 'Compare Sources' }}
    </Button>

    <!-- Comparison Results -->
    <div v-if="comparisonResult" class="mt-6 space-y-4">
      <div class="flex items-center gap-2">
        <CheckCircle class="w-5 h-5 text-green-500"/>
        <h4 class="font-semibold">Comparison Analysis</h4>
      </div>
      
      <!-- Side-by-side preview -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="text-center">
          <img 
            :src="selectedImages.source1.preview" 
            class="w-full max-h-24 object-contain rounded border mb-1"
          />
          <p class="text-xs text-muted-foreground">Source 1</p>
        </div>
        <div class="text-center">
          <img 
            :src="selectedImages.source2.preview" 
            class="w-full max-h-24 object-contain rounded border mb-1"
          />
          <p class="text-xs text-muted-foreground">Source 2</p>
        </div>
      </div>
      
      <div class="bg-muted rounded-lg p-4">
        <div class="prose prose-sm max-w-none" v-html="parseMarkdown(comparisonResult)"></div>
      </div>

      <!-- Actions -->
      <div class="flex gap-2">
        <Button 
          @click="compareImages" 
          variant="outline" 
          size="sm"
        >
          Re-analyze
        </Button>
        <Button 
          @click="clearComparison" 
          variant="outline" 
          size="sm"
        >
          Clear Results
        </Button>
        <Button 
          @click="exportComparison" 
          variant="outline" 
          size="sm"
        >
          Export Analysis
        </Button>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
      <div class="flex items-center gap-2 text-destructive">
        <AlertTriangle class="w-4 h-4"/>
        <span class="font-medium">Comparison Failed</span>
      </div>
      <p class="text-sm mt-1">{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import Button from './ui/Button.vue'
import Input from './ui/Input.vue'
import { GitCompare, Upload, X, Loader2, CheckCircle, AlertTriangle } from 'lucide-vue-next'
import { prepareImageForAPI, PromptTemplates } from '../utils/multimodal.js'

export default {
  name: 'SourceComparator',
  components: {
    Button,
    Input,
    GitCompare,
    Upload,
    X,
    Loader2,
    CheckCircle,
    AlertTriangle
  },
  setup() {
    const selectedImages = ref({
      source1: null,
      source2: null
    })
    
    const sourceUrls = ref({
      source1: '',
      source2: ''
    })
    
    const eventContext = ref('')
    const analysisType = ref('general')
    const isComparing = ref(false)
    const comparisonResult = ref('')
    const error = ref('')

    const canCompare = computed(() => {
      return selectedImages.value.source1 && selectedImages.value.source2
    })

    const handleImageUpload = (event, source) => {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        selectedImages.value[source] = {
          file: file,
          name: file.name,
          preview: URL.createObjectURL(file)
        }
        error.value = ''
      }
    }

    const removeImage = (source) => {
      if (selectedImages.value[source]?.preview) {
        URL.revokeObjectURL(selectedImages.value[source].preview)
      }
      selectedImages.value[source] = null
    }

    const compareImages = async () => {
      if (!canCompare.value) return

      isComparing.value = true
      error.value = ''

      try {
        // Prepare image data
        const image1Blob = await prepareImageForAPI(selectedImages.value.source1.file)
        const image2Blob = await prepareImageForAPI(selectedImages.value.source2.file)

        // Generate comparison prompt
        const prompt = generateComparisonPrompt()

        // Prepare multimodal inputs with both images
        const multimodalInputs = [
          {
            type: 'image',
            data: image1Blob
          },
          {
            type: 'image', 
            data: image2Blob
          }
        ]

        // Send to background script
        const response = await new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({
            action: 'promptAPI',
            prompt: prompt,
            multimodalInputs: multimodalInputs
          }, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response.success) {
          comparisonResult.value = response.response
        } else {
          throw new Error(response.error || 'Comparison failed')
        }

      } catch (err) {
        error.value = err.message
        console.error('Image comparison error:', err)
      } finally {
        isComparing.value = false
      }
    }

    const generateComparisonPrompt = () => {
      let context = eventContext.value || 'the same news event'
      
      if (sourceUrls.value.source1 || sourceUrls.value.source2) {
        context += '\n\nSources:\n'
        if (sourceUrls.value.source1) context += `Image 1: ${sourceUrls.value.source1}\n`
        if (sourceUrls.value.source2) context += `Image 2: ${sourceUrls.value.source2}\n`
      }

      let prompt = PromptTemplates.sourceComparison([], context)

      // Customize based on analysis type
      switch (analysisType.value) {
        case 'bias':
          prompt += '\n\nFocus particularly on editorial bias, selective framing, and how each source might be trying to influence viewer perception.'
          break
        case 'emotion':
          prompt += '\n\nFocus particularly on the emotional impact and mood conveyed by each image.'
          break
        case 'narrative':
          prompt += '\n\nFocus particularly on the story each image tells and what narrative elements are emphasized.'
          break
      }

      return prompt
    }

    const clearComparison = () => {
      comparisonResult.value = ''
      error.value = ''
    }

    const exportComparison = () => {
      if (!comparisonResult.value) return

      const exportData = {
        timestamp: new Date().toISOString(),
        eventContext: eventContext.value,
        analysisType: analysisType.value,
        sources: {
          source1: sourceUrls.value.source1 || 'Uploaded image',
          source2: sourceUrls.value.source2 || 'Uploaded image'
        },
        analysis: comparisonResult.value
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `source-comparison-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
    }

    // Simple markdown parser
    const parseMarkdown = (text) => {
      if (!text) return ''
      
      let html = text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
      
      return `<p>${html}</p>`
    }

    return {
      selectedImages,
      sourceUrls,
      eventContext,
      analysisType,
      isComparing,
      comparisonResult,
      error,
      canCompare,
      handleImageUpload,
      removeImage,
      compareImages,
      clearComparison,
      exportComparison,
      parseMarkdown
    }
  }
}
</script>

<style scoped>
.prose {
  color: inherit;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose strong {
  font-weight: 600;
}
</style>
