<template>
  <div class="image-analyzer">
    <!-- Image Analysis Header -->
    <div class="flex items-center gap-2 mb-4">
      <Camera class="w-5 h-5 text-primary"/>
      <h3 class="text-lg font-semibold">Visual Deep Dive</h3>
    </div>

    <!-- Image Selection Area -->
    <div v-if="!selectedImage" class="space-y-4">
      <p class="text-sm text-muted-foreground">
        Select an image from the current page to analyze in context of the article.
      </p>
      
      <!-- Page Images Grid -->
      <div v-if="pageImages.length > 0" class="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
        <div 
          v-for="image in pageImages" 
          :key="image.id"
          @click="selectImage(image)"
          class="relative cursor-pointer border-2 border-transparent hover:border-primary rounded-lg overflow-hidden transition-colors"
        >
          <img 
            :src="image.src" 
            :alt="image.alt"
            class="w-full h-20 object-cover"
            loading="lazy"
          />
          <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-opacity flex items-center justify-center">
            <Camera class="w-6 h-6 text-white opacity-0 hover:opacity-100 transition-opacity"/>
          </div>
        </div>
      </div>

      <!-- No Images Found -->
      <div v-else class="text-center py-8 text-muted-foreground">
        <Camera class="w-12 h-12 mx-auto mb-2 opacity-50"/>
        <p>No images found on this page</p>
      </div>

      <!-- Manual Image Upload -->
      <div class="border-2 border-dashed border-border rounded-lg p-4">
        <input 
          ref="fileInput"
          type="file" 
          accept="image/*" 
          @change="handleFileUpload"
          class="hidden"
        />
        <button 
          @click="$refs.fileInput.click()"
          class="w-full flex items-center justify-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Upload class="w-4 h-4"/>
          Upload image to analyze
        </button>
      </div>
    </div>

    <!-- Selected Image Analysis -->
    <div v-else class="space-y-4">
      <!-- Selected Image Preview -->
      <div class="relative">
        <img 
          :src="selectedImage.src" 
          :alt="selectedImage.alt"
          class="w-full max-h-48 object-contain rounded-lg border"
        />
        <button 
          @click="clearSelection"
          class="absolute top-2 right-2 p-1 bg-background border rounded-full hover:bg-muted transition-colors"
        >
          <X class="w-4 h-4"/>
        </button>
      </div>

      <!-- Analysis Controls -->
      <div class="space-y-2">
        <label class="text-sm font-medium">Analysis Focus:</label>
        <select 
          v-model="analysisType" 
          class="w-full p-2 border rounded-md bg-background"
        >
          <option value="general">General Analysis</option>
          <option value="factcheck">Fact-checking & Bias</option>
          <option value="mood">Mood & Emotional Tone</option>
          <option value="context">Article Context Match</option>
        </select>
      </div>

      <!-- Custom Analysis Prompt -->
      <div class="space-y-2">
        <label class="text-sm font-medium">Custom Question (Optional):</label>
        <Input 
          v-model="customPrompt"
          placeholder="Ask a specific question about this image..."
          class="w-full"
        />
      </div>

      <!-- Analyze Button -->
      <Button 
        @click="analyzeImage"
        :disabled="isAnalyzing"
        class="w-full"
      >
        <Loader2 v-if="isAnalyzing" class="w-4 h-4 mr-2 animate-spin"/>
        <Camera v-else class="w-4 h-4 mr-2"/>
        {{ isAnalyzing ? 'Analyzing...' : 'Analyze Image' }}
      </Button>
    </div>

    <!-- Analysis Results -->
    <div v-if="analysisResult" class="mt-6 space-y-4">
      <div class="flex items-center gap-2">
        <CheckCircle class="w-5 h-5 text-green-500"/>
        <h4 class="font-semibold">Analysis Complete</h4>
      </div>
      
      <div class="bg-muted rounded-lg p-4">
        <div class="prose prose-sm max-w-none" v-html="parseMarkdown(analysisResult)"></div>
      </div>

      <!-- Follow-up Actions -->
      <div class="flex gap-2">
        <Button 
          @click="analyzeImage" 
          variant="outline" 
          size="sm"
        >
          Re-analyze
        </Button>
        <Button 
          @click="clearAnalysis" 
          variant="outline" 
          size="sm"
        >
          Clear Results
        </Button>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
      <div class="flex items-center gap-2 text-destructive">
        <AlertTriangle class="w-4 h-4"/>
        <span class="font-medium">Analysis Failed</span>
      </div>
      <p class="text-sm mt-1">{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import Button from './ui/Button.vue'
import Input from './ui/Input.vue'
import { Camera, Upload, X, Loader2, CheckCircle, AlertTriangle } from 'lucide-vue-next'
import { prepareImageForAPI, PromptTemplates } from '../utils/multimodal.js'

export default {
  name: 'ImageAnalyzer',
  components: {
    Button,
    Input,
    Camera,
    Upload,
    X,
    Loader2,
    CheckCircle,
    AlertTriangle
  },
  props: {
    pageContent: {
      type: String,
      default: ''
    },
    pageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const pageImages = ref([])
    const selectedImage = ref(null)
    const analysisType = ref('general')
    const customPrompt = ref('')
    const isAnalyzing = ref(false)
    const analysisResult = ref('')
    const error = ref('')

    // Load page images on mount
    onMounted(async () => {
      await loadPageImages()
    })

    const loadPageImages = async () => {
      try {
        // Request images from content script
        const response = await new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({ action: 'getPageImages' }, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response && response.images) {
          pageImages.value = response.images
        }
      } catch (err) {
        console.error('Failed to load page images:', err)
      }
    }

    const selectImage = (image) => {
      selectedImage.value = image
      error.value = ''
    }

    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        selectedImage.value = {
          id: 'uploaded',
          src: URL.createObjectURL(file),
          alt: file.name,
          file: file
        }
        error.value = ''
      }
    }

    const clearSelection = () => {
      selectedImage.value = null
      analysisResult.value = ''
      error.value = ''
    }

    const clearAnalysis = () => {
      analysisResult.value = ''
      error.value = ''
    }

    const analyzeImage = async () => {
      if (!selectedImage.value) return

      isAnalyzing.value = true
      error.value = ''

      try {
        // Prepare image data
        let imageBlob
        if (selectedImage.value.file) {
          imageBlob = selectedImage.value.file
        } else {
          imageBlob = await prepareImageForAPI(selectedImage.value.src)
        }

        // Generate analysis prompt based on type
        let prompt = generateAnalysisPrompt()

        // Prepare multimodal inputs
        const multimodalInputs = [{
          type: 'image',
          data: imageBlob
        }]

        // Send to background script
        const response = await new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({
            action: 'promptAPI',
            prompt: prompt,
            context: props.pageContent,
            multimodalInputs: multimodalInputs
          }, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response.success) {
          analysisResult.value = response.response
        } else {
          throw new Error(response.error || 'Analysis failed')
        }

      } catch (err) {
        error.value = err.message
        console.error('Image analysis error:', err)
      } finally {
        isAnalyzing.value = false
      }
    }

    const generateAnalysisPrompt = () => {
      const baseContext = `Article: ${props.pageInfo.title || 'Current page'}\nContent: ${props.pageContent.substring(0, 1000)}...`
      
      let prompt = ''
      
      switch (analysisType.value) {
        case 'factcheck':
          prompt = PromptTemplates.imageAnalysis(baseContext, 'Focus on fact-checking and potential bias')
          break
        case 'mood':
          prompt = PromptTemplates.imageAnalysis(baseContext, 'Focus on emotional tone and mood')
          break
        case 'context':
          prompt = PromptTemplates.imageAnalysis(baseContext, 'Focus on how well this image matches the article context')
          break
        default:
          prompt = PromptTemplates.imageAnalysis(baseContext)
      }

      if (customPrompt.value.trim()) {
        prompt += `\n\nAdditional specific question: ${customPrompt.value}`
      }

      return prompt
    }

    // Simple markdown parser (reuse from main app)
    const parseMarkdown = (text) => {
      if (!text) return ''
      
      let html = text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
      
      return `<p>${html}</p>`
    }

    return {
      pageImages,
      selectedImage,
      analysisType,
      customPrompt,
      isAnalyzing,
      analysisResult,
      error,
      selectImage,
      handleFileUpload,
      clearSelection,
      clearAnalysis,
      analyzeImage,
      parseMarkdown
    }
  }
}
</script>

<style scoped>
.prose {
  color: inherit;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose strong {
  font-weight: 600;
}
</style>
