// Multimodal utilities for Chrome Prompt API
// Handles image, audio, and multi-input processing

/**
 * Convert various input types to Blob for Prompt API
 */
export async function prepareImageForAPI(imageInput) {
  if (imageInput instanceof Blob) {
    return imageInput;
  }
  
  if (imageInput instanceof File) {
    return imageInput;
  }
  
  if (typeof imageInput === 'string') {
    // Handle data URLs
    if (imageInput.startsWith('data:')) {
      const response = await fetch(imageInput);
      return await response.blob();
    }
    
    // Handle regular URLs
    try {
      const response = await fetch(imageInput);
      if (!response.ok) throw new Error(`Failed to fetch image: ${response.status}`);
      return await response.blob();
    } catch (error) {
      throw new Error(`Failed to load image from URL: ${error.message}`);
    }
  }
  
  if (imageInput instanceof HTMLImageElement) {
    return await imageElementToBlob(imageInput);
  }
  
  if (imageInput instanceof HTMLCanvasElement) {
    return await canvasToBlob(imageInput);
  }
  
  throw new Error('Unsupported image input type');
}

/**
 * Convert HTMLImageElement to Blob
 */
async function imageElementToBlob(img) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  canvas.width = img.naturalWidth || img.width;
  canvas.height = img.naturalHeight || img.height;
  
  ctx.drawImage(img, 0, 0);
  
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error('Failed to convert image to blob'));
      }
    }, 'image/jpeg', 0.9);
  });
}

/**
 * Convert HTMLCanvasElement to Blob
 */
async function canvasToBlob(canvas) {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error('Failed to convert canvas to blob'));
      }
    }, 'image/jpeg', 0.9);
  });
}

/**
 * Record audio using MediaRecorder API
 */
export class AudioRecorder {
  constructor() {
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.stream = null;
    this.isRecording = false;
  }

  async startRecording(options = {}) {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          ...options.audio
        } 
      });
      
      this.audioChunks = [];
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };
      
      this.mediaRecorder.start();
      this.isRecording = true;
      
      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw new Error(`Microphone access denied or not available: ${error.message}`);
    }
  }

  async stopRecording() {
    if (!this.mediaRecorder || !this.isRecording) {
      throw new Error('No active recording to stop');
    }

    return new Promise((resolve, reject) => {
      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm;codecs=opus' });
        this.cleanup();
        resolve(audioBlob);
      };

      this.mediaRecorder.onerror = (error) => {
        this.cleanup();
        reject(error);
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
  }
}

/**
 * Extract images from current page
 */
export function extractPageImages() {
  const images = [];
  const imageElements = document.querySelectorAll('img');
  
  imageElements.forEach((img, index) => {
    // Skip very small images (likely icons or decorative)
    if (img.naturalWidth < 100 || img.naturalHeight < 100) return;
    
    // Skip images without src
    if (!img.src) return;
    
    const rect = img.getBoundingClientRect();
    
    images.push({
      id: `img-${index}`,
      src: img.src,
      alt: img.alt || '',
      width: img.naturalWidth,
      height: img.naturalHeight,
      position: {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
        height: rect.height
      },
      element: img
    });
  });
  
  return images;
}

/**
 * Create multimodal prompt content array
 */
export function createMultimodalContent(textPrompt, mediaInputs = []) {
  const content = [];
  
  // Add text content
  if (textPrompt) {
    content.push({
      type: 'text',
      value: textPrompt
    });
  }
  
  // Add media inputs
  mediaInputs.forEach(input => {
    if (input.type === 'image' && input.data) {
      content.push({
        type: 'image',
        value: input.data
      });
    } else if (input.type === 'audio' && input.data) {
      content.push({
        type: 'audio',
        value: input.data
      });
    }
  });
  
  return content;
}

/**
 * Generate contextual prompts for different multimodal scenarios
 */
export const PromptTemplates = {
  imageAnalysis: (articleContext, imageDescription = '') => `
Based on the following article context, analyze this image and provide insights:

Article Context: ${articleContext}

Please analyze the image and address:
1. What key elements, mood, and visual narrative does this image convey?
2. How does the visual content relate to or support the article's main points?
3. Are there any potential biases or editorial choices in how this image is presented?
4. What questions or insights does this image raise about the topic?

${imageDescription ? `Additional context: ${imageDescription}` : ''}

Provide a detailed but concise analysis.`,

  voiceQuery: (articleContext, audioTranscription) => `
Based on the current article context, please respond to this voice query:

Article Context: ${articleContext}

Voice Query: "${audioTranscription}"

Please provide a helpful response that draws from the article content and addresses the user's question.`,

  sourceComparison: (images, context = '') => `
Compare these images from different news sources covering the same event or topic:

${context ? `Context: ${context}` : ''}

Please analyze:
1. Visual framing and composition differences
2. Emotional tone and mood conveyed by each image
3. What aspects of the story each image emphasizes or de-emphasizes
4. Potential editorial bias or perspective differences
5. How these visual choices might influence reader perception

Provide a balanced analysis of how different sources are presenting this story visually.`
};

/**
 * Validate multimodal inputs before sending to API
 */
export function validateMultimodalInputs(inputs) {
  const errors = [];
  
  inputs.forEach((input, index) => {
    if (!input.type) {
      errors.push(`Input ${index}: Missing type`);
      return;
    }
    
    if (!input.data) {
      errors.push(`Input ${index}: Missing data`);
      return;
    }
    
    if (input.type === 'image') {
      if (!(input.data instanceof Blob) && !(input.data instanceof File)) {
        errors.push(`Input ${index}: Image data must be Blob or File`);
      }
    } else if (input.type === 'audio') {
      if (!(input.data instanceof Blob) && !(input.data instanceof File)) {
        errors.push(`Input ${index}: Audio data must be Blob or File`);
      }
    }
  });
  
  if (errors.length > 0) {
    throw new Error(`Validation errors: ${errors.join(', ')}`);
  }
  
  return true;
}
