{"manifest_version": 3, "name": "<PERSON><PERSON> Prompt - AI Assistant", "version": "1.1.0", "description": "AI-powered assistant using Chrome's built-in Prompt API for webpage analysis and general assistance", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup.html", "default_title": "Aura Prompt AI Assistant", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["popup.html"], "matches": ["<all_urls>"]}]}